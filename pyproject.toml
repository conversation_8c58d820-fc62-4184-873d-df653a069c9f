[build-system]
requires = ["setuptools>=65.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "trxo"
version = "0.0.1"
description = "PingOne Advanced Identity Cloud Configuration Management Tool"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "TSL", email = "<EMAIL>"}
]
requires-python = ">=3.8"
keywords = ["forgerock", "identity", "configuration", "management", "cli", "PingOne","Advanced", "Identity", "Cloud", "trxo", "orchestrator"]
classifiers = [
    "Natural Language :: English",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "typer>=0.9.0",
    "rich>=13.0.0",
    "httpx>=0.23.0",
    "pyjwt==2.10.1",
    "jwcrypto==1.5.6",
    "tqdm==4.67.1",
    "py-file-versioning",
    "keyring",
    "deepdiff>=6.0.0",
    "GitPython>=3.1.0",
]


[project.urls]
Homepage = "https://github.com/techcanopysl/ping-aic-config-tool"
Repository = "https://github.com/techcanopysl/ping-aic-config-tool.git"
Documentation = "https://github.com/techcanopysl/ping-aic-config-tool/docs"

[project.scripts]
trxo = "trxo.main:app"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["trxo*"]

[tool.setuptools.package-data]
trxo = ["py.typed"]


[tool.isort]
profile = "black"
multi_line_mode = 3
include_trailing_comma = true
line_length = 100
skip_glob = ["*/migrations/*"]

