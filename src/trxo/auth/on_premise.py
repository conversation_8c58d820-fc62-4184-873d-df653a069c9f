"""
On-prem (AM) authentication helper.

Provides a simple way to obtain an AM session token (iPlanetDirectoryPro)
by calling the JSON authentication endpoint. This token is NOT persisted.
"""

from typing import Dict
import httpx


class OnPremAuth:
    def __init__(self, base_url: str, realm: str = "root"):
        self.base_url = base_url.rstrip("/")
        self.realm = realm.strip("/") or "root"

    @property
    def auth_url(self) -> str:
        from trxo.utils.url import construct_api_url
        endpoint = f"/am/json/realms/{self.realm}/authenticate"
        return construct_api_url(self.base_url, endpoint)

    def authenticate(self, username: str, password: str) -> Dict[str, str]:
        headers = {
            "Content-Type": "application/json",
            "Accept-API-Version": "resource=2.0, protocol=1.0",
            "X-OpenAM-Username": username,
            "X-OpenAM-Password": password,
        }
        try:
            with httpx.Client() as client:
                resp = client.post(self.auth_url, headers=headers, json={})
                resp.raise_for_status()
                data = resp.json()
                token_id = data.get("tokenId")
                if not token_id:
                    raise ValueError("No tokenId returned from AM authenticate endpoint")
                return {
                    "tokenId": token_id,
                    "successUrl": data.get("successUrl", ""),
                    "realm": data.get("realm", "/"),
                }
        except Exception as e:
            raise Exception(f"OnPrem authentication failed: {e}")
