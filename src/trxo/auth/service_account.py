import json
import time
import uuid
from typing import Dict
import jwt
from jwcrypto import jwk
import httpx


class ServiceAccountAuth:
    def __init__(self, jwk_path: str, client_id: str, sa_id: str, token_url: str, *, jwk_content: str | None = None):
        self.jwk_path = jwk_path
        self.jwk_content = jwk_content
        self.client_id = client_id
        self.sa_id = sa_id
        self.token_url = token_url

    def get_private_key(self) -> bytes:
        """Load JWK and convert to PEM format"""
        if self.jwk_content:
            jwk_data = json.loads(self.jwk_content)
        else:
            with open(self.jwk_path, "r", encoding="utf-8") as f:
                jwk_data = json.load(f)

        key = jwk.JWK(**jwk_data)
        return key.export_to_pem(private_key=True, password=None)

    def create_jwt(self) -> str:
        """Create signed JWT for authentication"""
        now = int(time.time())
        jwt_payload = {
            "iss": self.client_id,
            "sub": self.sa_id,
            "aud": self.token_url,
            "exp": now + 899,
            "jti": str(uuid.uuid4()),
        }

        private_key_pem = self.get_private_key()
        return jwt.encode(jwt_payload, private_key_pem, algorithm="RS256")

    def get_access_token(self) -> Dict:
        """Get access token using JWT assertion"""
        signed_jwt = self.create_jwt()

        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "client_id": "service-account",
            "grant_type": "urn:ietf:params:oauth:grant-type:jwt-bearer",
            "assertion": signed_jwt,
            "scope": (
                "fr:am:* "
                "fr:idm:* "
                "fr:idc:esv:* "
                # "fr:autoaccess:* "
                # "fr:idc:analytics:* "
                # "fr:idc:certificate:* "
                # "fr:idc:certificate:read "
                # "fr:idc:content-security-policy:* "
                # "fr:idc:content-security-policy:read "
                # "fr:idc:cookie-domain:* "
                # "fr:idc:cookie-domain:read "
                # "fr:idc:custom-domain:* "
                # "fr:idc:custom-domain:read "
                # "fr:idc:dataset:* "
                # "fr:idc:dataset:read "
                # "fr:idc:promotion:* "
                # "fr:idc:promotion:read "
                # "fr:idc:release:* "
                # "fr:idc:release:read "
                # "fr:idc:sso-cookie:* "
                # "fr:idc:sso-cookie:read "
                # "fr:iga:*"
            ),
        }
        try:
            with httpx.Client() as client:
                response = client.post(self.token_url, headers=headers, data=data)
                response.raise_for_status()
                return response.json()
        except Exception:
            raise Exception("Failed to get access token : ", response.text)
