import json
import html

def generate_side_by_side_diff(diff_result):
    """
    Renders a DeepDiff diff result into a clean, side-by-side HTML diff page.
    Each modified item is displayed in its own section.
    """
    html_blocks = []
    
    for item in diff_result.modified_items:
        dc = item.detailed_changes or {}
        reduced_curr = dc.get('reduced_current') or dc.get('current_item') or {}
        reduced_new = dc.get('reduced_new') or dc.get('new_item') or {}

        # Serialize JSON objects (stable text)
        curr_text = json.dumps(reduced_curr, indent=2, ensure_ascii=False)
        new_text = json.dumps(reduced_new, indent=2, ensure_ascii=False)

        # Create side-by-side HTML comparison
        left_lines = curr_text.splitlines()
        right_lines = new_text.splitlines()

        max_len = max(len(left_lines), len(right_lines))
        left_lines += [""] * (max_len - len(left_lines))
        right_lines += [""] * (max_len - len(right_lines))

        diff_html = []
        for left, right in zip(left_lines, right_lines):
            line_class = ""
            if left != right:
                line_class = "diff-modified"

            diff_html.append(f"""
                <tr class="{line_class}">
                    <td class="lineno">{left_lines.index(left) + 1}</td>
                    <td class="code-left">{html.escape(left)}</td>
                    <td class="lineno">{right_lines.index(right) + 1}</td>
                    <td class="code-right">{html.escape(right)}</td>
                </tr>
            """)

        safe_title = html.escape(item.item_name or item.item_id)
        table = f"""
        <div class='item-diff'>
            <h3>🧩 {safe_title}</h3>
            <table class='diff-table'>
                <thead>
                    <tr>
                        <th colspan='2'>Server</th>
                        <th colspan='2'>Import</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(diff_html)}
                </tbody>
            </table>
        </div>
        """
        html_blocks.append(table)

    final_html = f"""
    <html>
    <head>
        <meta charset="utf-8"/>
        <title>Side-by-Side JSON Diff</title>
        <style>
            body {{
                font-family: 'JetBrains Mono', monospace;
                background: #0d1117;
                color: #e6edf3;
                margin: 20px;
            }}
            h3 {{
                color: #58a6ff;
                margin-top: 2rem;
            }}
            .diff-table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 2rem;
                table-layout: fixed;
            }}
            th {{
                background: #161b22;
                color: #a0a0a0;
                padding: 8px;
                text-align: left;
                border-bottom: 2px solid #30363d;
            }}
            td {{
                vertical-align: top;
                padding: 4px 6px;
                border-bottom: 1px solid #30363d;
                word-wrap: break-word;
            }}
            .lineno {{
                width: 3%;
                color: #8b949e;
                text-align: right;
                padding-right: 6px;
                background: #161b22;
            }}
            .code-left, .code-right {{
                width: 47%;
                white-space: pre-wrap;
                background: #0d1117;
            }}
            .diff-modified .code-left {{
                background: #331e1e;
                color: #ff7b72;
            }}
            .diff-modified .code-right {{
                background: #19321e;
                color: #3fb950;
            }}
            .item-diff {{
                border: 1px solid #30363d;
                border-radius: 6px;
                box-shadow: 0 0 10px #161b22;
                padding: 10px;
                margin-bottom: 25px;
            }}
        </style>
    </head>
    <body>
        <h1>📘 Modified Items Diff Report</h1>
        {''.join(html_blocks)}
    </body>
    </html>
    """
    return final_html
