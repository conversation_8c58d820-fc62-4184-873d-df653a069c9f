import typer
from trxo.commands import config, project
from trxo.commands.imports import app as import_app
from trxo.commands.export import app as export_app
from trxo.commands.batch import app as batch_app

app = typer.Typer(
    help="[bold blue]TRxO[/bold blue] - PingOne Advanced Identity Cloud Configuration Management Tool",
    rich_markup_mode="rich",
)

# Add command groups
app.add_typer(config.app, name="config")
app.add_typer(project.app, name="project")
app.add_typer(export_app, name="export")
app.add_typer(import_app, name="import")
app.add_typer(batch_app, name="batch")

# Add standalone commands
app.command("projects")(project.list_projects)


@app.callback(invoke_without_command=True)
def callback(ctx: typer.Context):
    """
    [bold blue]TRxO[/bold blue] - PingOne Advanced Identity Cloud Configuration Management Tool

    A CLI tool for managing PingOne configurations across environments.
    """
    if not ctx.invoked_subcommand:
        print(
            "Welcome to the TRxO CLI! Manage your configurations effortlessly. To proceed type trxo --help"
        )


def main():
    app()


if __name__ == "__main__":
    main()
