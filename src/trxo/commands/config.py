import typer
from typing import Optional
from trxo.utils.config_store import ConfigStore
from trxo.utils.console import console, success, error, warning, info
from .config_modules.settings import get_credential_value, display_config
from .config_modules.auth_handler import setup_service_account_auth, setup_onprem_auth, normalize_base_url

app = typer.Typer(help="Manage project configuration")
config_store = ConfigStore()




@app.command()
def setup(
    jwk_path: Optional[str] = typer.Option(
        None, "--jwk-path", help="Path to JWK private key file"
    ),
    client_id: Optional[str] = typer.Option(None, "--client-id", help="Client ID"),
    sa_id: Optional[str] = typer.Option(None, "--sa-id", help="Service Account ID"),
    base_url: Optional[str] = typer.Option(
        None, "--base-url", help="Base URL for ForgeRock instance"
    ),
    auth_mode: Optional[str] = typer.Option(
        "service-account",
        "--auth-mode",
        help="Authentication mode: service-account (default) or onprem",
        case_sensitive=False,
    ),
    onprem_username: Optional[str] = typer.Option(
        None, "--onprem-username", help="On-Prem (AM) username"
    ),
    onprem_realm: Optional[str] = typer.Option(
        "root", "--onprem-realm", help="On-Prem realm (default: root)"
    ),
    regions: Optional[str] = typer.Option(None, "--regions", help="Regions"),
    storage_mode: Optional[str] = typer.Option(None, "--storage-mode", help="Storage mode: git (default) or local"),
    git_username: Optional[str] = typer.Option(None, "--git-username", help="Git username"),
    git_repo: Optional[str] = typer.Option(None, "--git-repo", help="Git repository name"),
    git_token: Optional[str] = typer.Option(None, "--git-token", help="Git token"),
):
    """Configure authentication for current project"""
    current_project = config_store.get_current_project()

    if not current_project:
        error("No active project. Run 'trxo project create <name>' first")
        raise typer.Exit(1)

    # Get existing config
    existing_config = config_store.get_project_config(current_project) or {}

    has_existing_config = existing_config.get("base_url") is not None

    # Check if configuration already exists
    if has_existing_config and not any([jwk_path, client_id, sa_id, base_url, onprem_username]):
        info(f"Found existing configuration for project '{current_project}'")
        info("You can override specific values using command-line arguments, example: --base-url https://new-url.com")
        raise typer.Exit(1)

    console.print()  # spacing
    info(f"Configuring project: [bold]{current_project}[/bold]")
    console.print()

    if not any([jwk_path, client_id, sa_id, base_url, onprem_username]):
        # Explain what we're doing
        warning("No saved configuration found or arguments provided.")
        info("Please enter your ForgeRock credentials.")
        console.print()

    # Get base URL first (common for both modes)
    base_url_value = get_credential_value(
        base_url, "base_url", existing_config, "Base URL for ForgeRock instance"
    )
    console.print()

    # Optional fields
    regions_value = get_credential_value(
        regions,
        "regions",
        existing_config,
        "Regions (comma-separated)",
        required=False,
    )

    storage_mode_value = get_credential_value(
        storage_mode,
        "storage_mode",
        existing_config,
        "\nStorage mode (git|local)",
        required=False,
    )

    console.print()

    # Save auth mode
    auth_mode_value = (auth_mode or "service-account").lower().strip()

    # Normalize base_url based on auth_mode
    base_url_value = normalize_base_url(base_url_value, auth_mode_value)

    if auth_mode_value == "service-account":
        config = setup_service_account_auth(
            existing_config=existing_config,
            jwk_path=jwk_path,
            client_id=client_id,
            sa_id=sa_id,
            base_url=base_url_value,
            regions=regions_value,
            storage_mode=storage_mode_value,
            git_username=git_username,
            git_repo=git_repo,
            git_token=git_token,
            current_project=current_project
        )

    elif auth_mode_value == "onprem":
        config = setup_onprem_auth(
            existing_config=existing_config,
            onprem_username=onprem_username,
            onprem_realm=onprem_realm,
            base_url=base_url_value,
            storage_mode=storage_mode_value,
            git_username=git_username,
            git_repo=git_repo,
            git_token=git_token,
            current_project=current_project
        )
    else:
        error("Invalid --auth-mode. Use 'service-account' or 'onprem'")
        raise typer.Exit(1)

    # Update project config
    existing_config.update(config)
    config_store.save_project(current_project, existing_config)

    console.print()
    success(f"Configuration saved for project '{current_project}'")


@app.command("show")
def show():
    """Show current project configuration"""
    current_project = config_store.get_current_project()

    if not current_project:
        error("No active project")
        raise typer.Exit(1)

    config = config_store.get_project_config(current_project)
    display_config(current_project, config)