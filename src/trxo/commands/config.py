import os
import json
import typer
from typing import Optional
from pathlib import Path
from rich.prompt import Prompt
from trxo.utils.config_store import ConfigStore
from trxo.auth.service_account import ServiceAccountAuth
from trxo.utils.console import console, success, error, warning, info, display_panel
from trxo.utils.git_manager import validate_and_setup_git_repo

# token endpoint to get access token
TOKEN_ENDPOINT = "/am/oauth2/access_token"

app = typer.Typer(help="Manage project configuration")
config_store = ConfigStore()


def get_credential_value(
    arg_value: Optional[str],
    config_key: str,
    existing_config: dict,
    prompt_text: str,
    required: bool = True,
) -> Optional[str]:
    """Get credential value with priority: argument > config file > prompt"""
    if arg_value:
        return arg_value

    if existing_config and config_key in existing_config:
        return existing_config[config_key]

    if required:
        return Prompt.ask(prompt_text)
    else:
        return Prompt.ask(prompt_text, default="")


def test_authentication(auth: ServiceAccountAuth) -> bool:
    """Test authentication"""
    # try:
    auth.get_access_token()
    return True
    # except Exception as e:
    #     raise e


@app.command()
def setup(
    jwk_path: Optional[str] = typer.Option(
        None, "--jwk-path", help="Path to JWK private key file"
    ),
    client_id: Optional[str] = typer.Option(None, "--client-id", help="Client ID"),
    sa_id: Optional[str] = typer.Option(None, "--sa-id", help="Service Account ID"),
    base_url: Optional[str] = typer.Option(
        None, "--base-url", help="Base URL for ForgeRock instance"
    ),
    auth_mode: Optional[str] = typer.Option(
        "service-account",
        "--auth-mode",
        help="Authentication mode: service-account (default) or onprem",
        case_sensitive=False,
    ),
    onprem_username: Optional[str] = typer.Option(
        None, "--onprem-username", help="On-Prem (AM) username"
    ),
    onprem_realm: Optional[str] = typer.Option(
        "root", "--onprem-realm", help="On-Prem realm (default: root)"
    ),
    regions: Optional[str] = typer.Option(None, "--regions", help="Regions"),
    storage_mode: Optional[str] = typer.Option(None, "--storage-mode", help="Storage mode: git (default) or local"),
    git_username: Optional[str] = typer.Option(None, "--git-username", help="Git username"),
    git_repo: Optional[str] = typer.Option(None, "--git-repo", help="Git repository name"),
    git_token: Optional[str] = typer.Option(None, "--git-token", help="Git token"),
):
    """Configure authentication for current project"""
    current_project = config_store.get_current_project()

    if not current_project:
        error("No active project. Run 'trxo project create <name>' first")
        raise typer.Exit(1)

    # Get existing config
    existing_config = config_store.get_project_config(current_project) or {}

    check_existance = existing_config.get("base_url") is not None

    # After getting existing_config, add:
    if  check_existance and not any([jwk_path, client_id, sa_id, base_url, onprem_username]):
        info(f"Found existing configuration for project '{current_project}'")
        info("You can override specific values using command-line arguments, example: --base-url https://new-url.com")
        raise typer.Exit(1)

    console.print()  # spacing
    info(f"Configuring project: [bold]{current_project}[/bold]")
    console.print()

    if not any([jwk_path, client_id, sa_id, base_url, onprem_username]):
        # Explain what we're doing
        warning("No saved configuration found or arguments provided.")
        info("Please enter your ForgeRock credentials.")
        console.print()



    # Get base URL first (common for both modes)
    base_url_value = get_credential_value(
        base_url, "base_url", existing_config, "Base URL for ForgeRock instance"
    )
    console.print()

    # Optional fields
    regions_value = (
        get_credential_value(
            regions,
            "regions",
            existing_config,
            "Regions (comma-separated)",
            required=False,
        )
    )

    storage_mode_value = (
        get_credential_value(
            storage_mode,
            "storage_mode",
            existing_config,
            "\nStorage mode (git|local)",
            # required=True,
        )
    )

    console.print()

    # Save auth mode
    auth_mode_value = (auth_mode or "service-account").lower().strip()

    # Normalize base_url based on auth_mode
    if base_url_value:
        base_url_value = base_url_value.rstrip("/")
        if auth_mode_value == "service-account":
            # If user enters https://host/am, strip /am to keep base (SA usually expects root base + /am endpoint)
            if base_url_value.endswith("/am"):
                base_url_value = base_url_value[:-3]
        elif auth_mode_value == "onprem":
            # For on-prem, if no context is provided (e.g. https://host), default to /am
            # If context is provided (e.g. https://host/custom), keep it.
            from urllib.parse import urlparse
            parsed = urlparse(base_url_value)
            if not parsed.path or parsed.path == "/":
                base_url_value = f"{base_url_value}/am"

    if auth_mode_value == "service-account":
        # Collect SA-only inputs
        jwk_path_value = get_credential_value(
            jwk_path, "jwk_path", existing_config, "JWK private key file path"
        )
        console.print()

        # Validate JWK path and load content for secure storage
        jwk_path_expanded = os.path.expanduser(jwk_path_value)
        if not os.path.exists(jwk_path_expanded):
            error(f"JWK file not found at {jwk_path_expanded}")
            raise typer.Exit(1)
        try:
            with open(jwk_path_expanded, "r", encoding="utf-8") as f:
                jwk_raw = f.read()
        except Exception as e:
            error(f"Failed to read JWK file: {e}")
            raise typer.Exit(1)

        # Derive metadata: kid (if present) and fingerprint
        kid_value = None
        jwk_fingerprint = None
        try:
            jwk_obj = json.loads(jwk_raw)
            kid_value = jwk_obj.get("kid")
            normalized = json.dumps(jwk_obj, sort_keys=True, separators=(",", ":")).encode("utf-8")
            import hashlib  # local import to avoid top-level impact
            jwk_fingerprint = "sha256:" + hashlib.sha256(normalized).hexdigest()
        except Exception:
            pass

        # Persist secret in keyring (best effort); do not write JWK into config
        keyring_ok = False
        try:
            import keyring  # optional backend
            keyring.set_password(f"trxo:{current_project}:jwk", "jwk", jwk_raw)
            keyring_ok = True
        except Exception:
            keyring_ok = False

        client_id_value = get_credential_value(
            client_id, "client_id", existing_config, "Client ID"
        )
        console.print()

        sa_id_value = get_credential_value(
            sa_id, "sa_id", existing_config, "Service Account ID"
        )
        console.print()

        # Construct token URL from base URL
        # Ensure we use normalized base_url_value which might have changed
        token_url = base_url_value.rstrip("/") + TOKEN_ENDPOINT

        # Validate JWK path
        jwk_path_expanded = os.path.expanduser(jwk_path_value)
        if not os.path.exists(jwk_path_expanded):
            error(f"JWK file not found at {jwk_path_expanded}")
            raise typer.Exit(1)
        
        if storage_mode_value == "git":
            git_username_value = get_credential_value(
                git_username, "git_username", existing_config, "\nGit username"
            )
            git_repo_value = get_credential_value(
                git_repo, "git_repo", existing_config, "\nGit Repository URL (https://github.com/owner/repo.git)"
            )
            git_token_value = get_credential_value(
                git_token, "git_token", existing_config, "\nPersonal access token"
            )

            try:
                # validate_git_credentials(git_username_value, git_repo_value, git_token_value)
                repo = validate_and_setup_git_repo(git_username_value, git_token_value, git_repo_value)

                config_store.store_git_credentials(current_project, git_username_value, git_repo_value, git_token_value)

                git_credentials = config_store.get_git_credentials()

                # success("Git credentials validated successfully!")
            except Exception as e:
                error(f"Git credentials validation failed: {str(e)}")
                raise typer.Exit(1)

        # Test SA authentication
        try:
            # Prefer in-memory JWK content for testing to avoid relying on file
            auth = ServiceAccountAuth(
                jwk_path_expanded, client_id_value, sa_id_value, token_url, jwk_content=jwk_raw
            )
            test_result = test_authentication(auth)
            if test_result:
                success("Authentication test successful!")
            else:
                error("Authentication test failed")
                raise typer.Exit(1)
        except Exception as e:
            error(f"Authentication test failed: {str(e)}")
            raise typer.Exit(1)

        # Save configuration (keyring-only for secret content)
        if storage_mode_value == "git":
            config = {
                "auth_mode": "service-account",
                "base_url": base_url_value,
                "sa_id": sa_id_value,
                "jwk_path": jwk_path_expanded,
                "jwk_keyring": keyring_ok,
                # "jwk_kid": kid_value,
                "jwk_fingerprint": jwk_fingerprint,
                # "client_id": client_id_value,
                "token_url": token_url,
                # "regions": [r.strip() for r in regions_value split(",") if r.strip()],
                "regions": (
                    regions_value if isinstance(regions_value, list)
                    else [r.strip() for r in regions_value.split(",") if r.strip()] if regions_value
                    else []
                ),
                "storage_mode": storage_mode_value,
                "git_username": git_username_value,
                "git_repo": git_repo_value,
                "client_id": client_id_value,
            }
        else:
            config = {
                "auth_mode": "service-account",
                "sa_id": sa_id_value,               
                "base_url": base_url_value,
                "jwk_path": jwk_path_expanded,
                "jwk_keyring": keyring_ok,
                # "jwk_kid": kid_value,
                "jwk_fingerprint": jwk_fingerprint,
                "client_id": client_id_value,
                "token_url": token_url,
                "regions": (
                    regions_value if isinstance(regions_value, list)
                    else [r.strip() for r in regions_value.split(",") if r.strip()] if regions_value
                    else []
                ),
                "storage_mode": storage_mode_value,
            }

    elif auth_mode_value == "onprem":
        # For on-prem, we only store non-sensitive fields
        from trxo.auth.onprem import OnPremAuth
        username_value = get_credential_value(
            onprem_username, "onprem_username", existing_config, "On-Prem username"
        )
        realm_value = get_credential_value(
            onprem_realm, "onprem_realm", existing_config, "On-Prem realm", required=False
        ) or "root"

        # Prompt for password (not stored)
        console.print()
        import getpass
        password_value = getpass.getpass("On-Prem password: ")

        if storage_mode_value == "git":
            git_username_value = get_credential_value(
                git_username, "git_username", existing_config, "\nGit username"
            )
            git_repo_value = get_credential_value(
                git_repo, "git_repo", existing_config, "\nGit Repository URL (https://github.com/owner/repo.git)"
            )
            git_token_value = get_credential_value(
                git_token, "git_token", existing_config, "\nPersonal access token"
            )

            try:
                print()
                # validate_git_credentials(git_username_value, git_repo_value, git_token_value)
                repo = validate_and_setup_git_repo(git_username_value, git_token_value, git_repo_value)

                config_store.store_git_credentials(current_project, git_username_value, git_repo_value, git_token_value)

                git_credentials = config_store.get_git_credentials()

                # success("Git credentials validated successfully!")
            except Exception as e:
                error(f"Git credentials validation failed: {str(e)}")
                raise typer.Exit(1)

        # Test On-Prem authentication
        try:
            info("\nTesting On-Prem authentication (password will NOT be stored)")
            client = OnPremAuth(base_url=base_url_value, realm=realm_value)
            data = client.authenticate(username=username_value, password=password_value)
            if data.get("tokenId"):
                success("On-Prem authentication test successful!")
            else:
                error("On-Prem authentication test failed")
                raise typer.Exit(1)
        except Exception as e:
            error(f"On-Prem authentication test failed: {str(e)}")
            raise typer.Exit(1)

        if storage_mode_value == "git":
            config = {
                "auth_mode": "onprem",
                "base_url": base_url_value,
                "onprem_username": username_value,
                "onprem_realm": realm_value,
                # "regions": [r.strip() for r in regions_value.split(",") if r.strip()],
                "storage_mode": storage_mode_value,
                "git_username": git_username_value,
                "git_repo": git_repo_value,
            }
        else:
            config = {
                "auth_mode": "onprem",
                "base_url": base_url_value,
                "onprem_username": username_value,
                "onprem_realm": realm_value,
                # "regions": [r.strip() for r in regions_value.split(",") if r.strip()],
                "storage_mode": storage_mode_value,
            }
    else:
        error("Invalid --auth-mode. Use 'service-account' or 'onprem'")
        raise typer.Exit(1)

    # Update project config
    existing_config.update(config)
    config_store.save_project(current_project, existing_config)

    console.print()
    success(f"Configuration saved for project '{current_project}'")
    # info(f"Token URL: {token_url}")


@app.command("show")
def show():
    """Show current project configuration"""
    current_project = config_store.get_current_project()

    if not current_project:
        error("No active project")
        raise typer.Exit(1)

    config = config_store.get_project_config(current_project)
    if not config:
        warning(f"No configuration found for project '{current_project}'")
        return

    # Hide sensitive information
    safe_config = config.copy()

    if "jwk_path" in safe_config:
        safe_config["jwk_path"] = Path(safe_config["jwk_path"]).name

    # New: never show JWK content; show only keyring status, kid, fingerprint
    if "jwk" in safe_config:
        del safe_config["jwk"]
    if "jwk_keyring" in safe_config:
        safe_config["jwk_keyring"] = bool(safe_config["jwk_keyring"])
    if "jwk_kid" in safe_config and safe_config["jwk_kid"]:
        # mask kid partially
        kid = str(safe_config["jwk_kid"])
        if len(kid) > 6:
            safe_config["jwk_kid"] = kid[:3] + "*" * (len(kid) - 6) + kid[-3:]

    config_text = "\n".join([f"{key}: {value}" for key, value in safe_config.items()])
    display_panel(config_text, f"Configuration for '{current_project}'", "blue")