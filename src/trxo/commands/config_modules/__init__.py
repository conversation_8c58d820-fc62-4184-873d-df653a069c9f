"""
Configuration management module.

This module provides structured configuration management functionality
split into logical components:
- settings: User settings and credential management
- validation: Configuration and authentication validation
- auth_handler: Authentication setup and handling
"""

from .settings import get_credential_value, display_config
from .validation import validate_authentication, validate_jwk_file, validate_git_setup
from .auth_handler import setup_service_account_auth, setup_onprem_auth

__all__ = [
    'get_credential_value',
    'display_config', 
    'validate_authentication',
    'validate_jwk_file',
    'validate_git_setup',
    'setup_service_account_auth',
    'setup_onprem_auth'
]
