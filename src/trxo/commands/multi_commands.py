"""
Multi-command utilities for handling multiple exports/imports in single commands.

Provides helper functions to extend existing commands with multi-file capabilities.
"""

import typer
from typing import List, Optional
from trxo.utils.console import info, success, error


def parse_multi_commands(commands_str: str) -> List[str]:
    """Parse comma-separated command string into list"""
    return [cmd.strip() for cmd in commands_str.split(",") if cmd.strip()]


def parse_multi_files(files_str: str) -> List[str]:
    """Parse comma-separated file string into list"""
    return [file.strip() for file in files_str.split(",") if file.strip()]


def validate_command_file_pairs(commands: List[str], files: List[str]) -> bool:
    """Validate that commands and files lists match"""
    if len(commands) != len(files):
        error(f"Number of commands ({len(commands)}) must match number of files ({len(files)})")
        return False
    return True


def create_multi_export_wrapper(single_export_func):
    """Wrapper to add multi-command support to existing export functions"""
    def multi_export_wrapper(
        commands: Optional[str] = None,
        single_command: Optional[str] = None,
        **kwargs
    ):
        if commands:
            # Multi-command mode
            cmd_list = parse_multi_commands(commands)
            info(f"Multi-export mode: {len(cmd_list)} commands")
            
            success_count = 0
            for i, cmd in enumerate(cmd_list, 1):
                print("-"*50)
                info(f"[{i}/{len(cmd_list)}] Exporting {cmd}...")
                try:
                    # Call the original function with the specific command
                    single_export_func(command=cmd, **kwargs)
                    success(f"{cmd} exported successfully")
                    success_count += 1
                except Exception as e:
                    error(f"Failed to export {cmd}: {e}")
            
            info(f"Multi-export completed: {success_count}/{len(cmd_list)} successful")
        else:
            # Single command mode (original behavior)
            single_export_func(**kwargs)
    
    return multi_export_wrapper


def create_multi_import_wrapper(single_import_func):
    """Wrapper to add multi-command support to existing import functions"""
    def multi_import_wrapper(
        commands: Optional[str] = None,
        files: Optional[str] = None,
        file: Optional[str] = None,
        **kwargs
    ):
        if commands and files:
            # Multi-command mode
            cmd_list = parse_multi_commands(commands)
            file_list = parse_multi_files(files)
            
            if not validate_command_file_pairs(cmd_list, file_list):
                raise typer.Exit(1)
            
            info(f"Multi-import mode: {len(cmd_list)} commands")
            
            success_count = 0
            for i, (cmd, file_path) in enumerate(zip(cmd_list, file_list), 1):
                info(f"[{i}/{len(cmd_list)}] Importing {cmd} from {file_path}...")
                try:
                    # Call the original function with the specific command and file
                    single_import_func(file=file_path, **kwargs)
                    success(f"{cmd} imported successfully")
                    success_count += 1
                except Exception as e:
                    error(f"Failed to import {cmd}: {e}")
            
            info(f"Multi-import completed: {success_count}/{len(cmd_list)} successful")
        else:
            # Single command mode (original behavior)
            single_import_func(file=file, **kwargs)
    
    return multi_import_wrapper


# Example usage documentation
MULTI_COMMAND_EXAMPLES = """
## Multi-Command Examples

### Batch Export:
```bash
# Export multiple configurations
trxo batch export realms services themes --dir batch_exports

# Export with specific scope/realm
trxo batch export services policies --scope realm --realm alpha --dir exports

# View multiple configurations in tables
trxo batch export realms themes managed --view
```

### Batch Import:
```bash
# Generate config template
trxo batch generate-config --type import --output my_imports.json

# Edit my_imports.json, then:
trxo batch import my_imports.json

# Dry run to see what would be imported
trxo batch import my_imports.json --dry-run
```

### Config File Format (my_imports.json):
```json
{
  "description": "My batch import configuration",
  "imports": [
    {"command": "services", "file": "services_export.json", "scope": "global"},
    {"command": "policies", "file": "policies_export.json", "scope": "realm", "realm": "alpha"},
    {"command": "managed", "file": "managed_export.json"}
  ]
}
```

### Alternative: Enhanced Individual Commands
```bash
# If we enhance individual commands (future approach):
trxo export services,themes,managed --dir exports
trxo import services,policies --files services.json,policies.json
```
"""
