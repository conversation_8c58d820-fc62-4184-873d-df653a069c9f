# PingOne Advanced Identity Cloud Configuration Management Tool Specification - **1.0-DRAFT**

## 1. Overview

The PingOne Advanced Identity Cloud Configuration Management Tool is designed to streamline the process of managing configurations across sandbox and development environments in multiple regions. The tool enables developers to create and test configurations in a sandbox environment, extract these configurations, compare them with the development environment, resolve conflicts (automatically or manually), and apply the changes to the target development environment in a specified region. The tool supports both dynamic and static configurations and is implemented as a command-line utility for integration with CI/CD pipelines.

## 2. Objectives

- Enable developers to create and test configurations in a sandbox environment.
- Extract configurations from the sandbox and compare them with the development environment.
- Provide intelligent conflict resolution (similar to Git merge/pull) with automatic or manual options.
- Apply resolved configurations to the development environment in a specified region.
- Support multi-region deployments with region-specific configuration handling.
- Handle both dynamic (Environment-Specific Variables, ESVs) and static configurations.
- Ensure compatibility with PingOne Advanced Identity Cloud APIs and security best practices.

## 3. Functional Requirements

### 3.1 Configuration Extraction
- **Description**: Extract configurations from the sandbox and development environments using the PingOne Advanced Identity Cloud REST API.
- **Inputs**:
  - API key and secret for authentication.- TBD ????
  - Sandbox and development environment IDs.
  - Target region (e.g., `lon1` for London).
  - Optional configuration type (e.g., `users`, `policies`, or `all`).
- **Output**: JSON representation of configurations for both environments.
- **API Endpoint**: `GET /v1/environments/{environmentId}/configurations`.
- **Authentication**: Use API key and secret for read-only operations.

### 3.2 Configuration Comparison
- **Description**: Compare sandbox and development environment configurations to identify differences.
- **Method**: Use Python’s `difflib` to generate a unified diff of JSON configurations.
- **Output**: A list of differences highlighting additions, deletions, and modifications.
- **Considerations**: Handle nested JSON structures and environment-specific variables (ESVs).

### 3.3 Conflict Resolution
- **Description**: Resolve differences between sandbox and development configurations.
- **Modes**:
  - **Automatic Resolution**: Prioritize sandbox configuration as the source of truth.
  - **Manual Resolution**: Display differences and prompt the user to accept or reject sandbox changes.
- **Output**: Resolved configuration in JSON format.
- **Considerations**: Ensure resolution respects dynamic and static configuration constraints.

### 3.4 Configuration Application
- **Description**: Apply the resolved configuration to the development environment.
- **API Endpoint**: `PUT /v1/environments/{environmentId}/configurations`.
- **Authentication**: Use an access token obtained via `POST /v1/authenticate`.
- **Considerations**: Validate configurations before application to prevent errors.

### 3.5 Multi-Region Support
- **Description**: Support configuration management across multiple regions (e.g., `lon1` for London, `us1` for North America).
- **Implementation**: Construct region-specific API endpoints (e.g., `https://openam-<env>-<region>.forgeblocks.com`).
- **Input**: Region code provided as a command-line argument.


## Logical Architecture 

**DRAFT**

```mermaid
graph LR
    CLI[Command Line Interface] -->|Initiates| CM[Configuration Manager]
    CM -->|Makes API requests| API[PingOne API Client]
    CM -->|Stores/retrieves configs| CS[Configuration Store]
    CM -->|Resolves differences| CR[Conflict Resolution Engine]
    API -->|Extracts configurations| SE[Sandbox Environment]
    API -->|Extracts/applies configurations| DE[Development Environment]
    CS -->|Provides configs| CR
    SE -->|Stores extracted configs| CS
    DE -->|Stores extracted configs| CS
    CR -->|Stores resolved configs| CS

    classDef component fill:#f9f,stroke:#333,stroke-width:2px;
    class CLI,CM,API,CS,CR,SE,DE component;

    %% Notes for clarity
    noteCLI[Accepts user inputs API key, environment IDs, region, auto-resolve flag]
    noteCM[Orchestrates configuration extraction, comparison, resolution, and application]
    noteAPI[Handles authentication and region-specific API calls e.g., openam-<env>-<region>.forgeblocks.com]
    noteSE[Mutable environment with static and dynamic configurations]
    noteDE[Target environment for applying resolved static and dynamic configurations]
    noteCS[Holds JSON configurations for sandbox, development, and resolved states]
    noteCR[Supports automatic or manual user input conflict resolution]

    %% Styling notes to associate with components
    noteCLI --> CLI
    noteCM --> CM
    noteAPI --> API
    noteSE --> SE
    noteDE --> DE
    noteCS --> CS
    noteCR --> CR
```
## 4. Handling Dynamic and Static Configurations

### 4.1 Static Configurations
- **Definition**: Configurations that do not change based on the environment, such as user schemas, authentication policies, or static roles.
- **Handling**:
  - Extracted as JSON objects from the sandbox and development environments.
  - Compared using `difflib` to identify differences.
  - Applied directly to the development environment via the API.
- **Conflict Resolution**:
  - Automatic: Overwrite development configuration with sandbox configuration.
  - Manual: Prompt user to choose between sandbox and development values.
- **Example**: A user schema defining attribute names and types.

### 4.1.1 Static Configurations - In Scope
**TBD**

### 4.1.2 Static Configurations - Out of Scope
**TBD**

### 4.2 Dynamic Configurations (Environment-Specific Variables, ESVs)
- **Definition**: Configurations that vary by environment, such as API endpoints, secrets, or region-specific settings.
- **Handling**:
  - Identify ESVs during extraction using the PingOne API’s ESV-specific endpoints (`/v1/environments/{environmentId}/esvs`).
  - Store ESVs in a separate JSON structure to distinguish them from static configurations.
  - During comparison, flag ESVs for special handling to avoid overwriting region-specific values.
  - During application, merge ESVs with static configurations, ensuring region-specific values are preserved or updated as needed.
- **Conflict Resolution**:
  - Automatic: Preserve development environment ESVs unless explicitly overridden by sandbox ESVs.
  - Manual: Prompt user to review and select ESV values, highlighting region-specific differences.
- **Example**: A region-specific OAuth client secret or a database connection string.

### 4.2.1 Dynamic Configurations - In Scope
**TBD**

### 4.2.2 Dynamic Configurations - Out of Scope
**TBD**


### 4.3 Implementation Details
- **Extraction**: Use separate API calls for static configurations and ESVs to ensure accurate handling.
- **Comparison**: Tag ESVs in the diff output to indicate dynamic nature.
- **Application**: Validate ESVs against region-specific constraints before applying (e.g., ensure a London-specific endpoint is not applied to a US environment).

## 5. Command-Line Utility Implementation


### 5.1 Prerequisites
- **Python Version**: 3.8 or higher.
- **Dependencies**: Install `requests` and `difflib` (standard library) via `pip install requests`.
- **Credentials**: API key and secret from the PingOne Advanced Identity Cloud admin console.
- **Environment IDs**: Sandbox and development environment IDs.
- **Region Codes**: Valid region codes (e.g., `lon1`, `us1`).



### 5.2 Command-Line Options and Arguments

```
$> trxo config project -> configure a new project
$> trxo projects ls -> projects -> json configurations -> ~/.trxo/config
$> trxo project <project_name> -> put that project configurations in the context
$> trxo config -> configs - login url, credentials, regions, sandbox env url, region based dev url -> ./trxo/projects/<project_name>/config
$> trxo login -> get your token and store that token in the ./trxo/projects/<project_name>/config
$> trxo export all <specific components> --region <region name> --env <environment_specific> -> exports all the static configurations
$> trxo export all <specific components> -> exports all the static configurations -> default dump all sandbox config
$> trxo merge dry-run <specific components> -> compare the configs from sandbox to dev and see if the configs are ok to be exported
$> trxo import dry-run <specific components> -> compare the configs from sandbox to dev and see if the configs are ok to be exported
$> trxo import all <specific components> -> import from sandbox to dev urls

```




### 5.3 Configuration for Sandbox and Multi-Region Support
- **Sandbox Configuration**:
  - The sandbox environment is treated as mutable, allowing developers to create and modify configurations.
  - Configurations are extracted using the PingOne API with the sandbox environment ID.
  - The tool uses the region-specific base URL (e.g., `https://openam-<sandbox-env-id>-<region>.forgeblocks.com`).
- **Multi-Region Configuration**:
  - The tool constructs API endpoints dynamically based on the provided region code.
  - Region-specific ESVs are identified and preserved during conflict resolution to avoid overwriting region-specific settings.
  - The `--region` argument determines the target development environment’s API endpoint.
  - Example: For region `lon1`, the tool uses `https://openam-<dev-env-id>-lon1.forgeblocks.com`.

### 5.4 Workflow
1. **Authenticate**: Use API key and secret to obtain an access token for write operations.
2. **Extract Configurations**: Retrieve static and dynamic configurations from sandbox and development environments.
3. **Compare Configurations**: Generate a unified diff, tagging ESVs for special handling.
4. **Resolve Conflicts**: Apply automatic or manual resolution, preserving region-specific ESVs as needed.
5. **Apply Configurations**: Use the API to update the development environment with the resolved configuration.

## 6. Non-Functional Requirements

- **Security**:
  - Store API keys and secrets securely (e.g., as environment variables or in a secure vault).
  - Use HTTPS for all API communications.
- **Performance**:
  - Optimize API calls to minimize latency (e.g., cache access tokens).
  - Handle large configuration sets efficiently during comparison.
- **Scalability**:
  - Support multiple regions and environments without code changes.
  - Integrate with CI/CD pipelines for automated deployments.
- **Error Handling**:
  - Provide clear error messages for authentication failures, API errors, or invalid configurations.
  - Validate configurations before application to prevent runtime errors.

## 7. Assumptions and Constraints

- **Assumptions**:
  - The PingOne Advanced Identity Cloud API is available and supports the required endpoints.
  - Sandbox environments are mutable, and development environments allow configuration updates.
  - Users have valid API credentials with appropriate permissions.
- **Constraints**:
  - The tool relies on the PingOne API, which may have rate limits or downtime.
  - Dynamic configurations (ESVs) require special handling to avoid overwriting region-specific settings.
  - The tool is designed as a command-line utility and does not include a GUI.

## 8. Future Enhancements

- Integrate with the PingOne Advanced Identity Cloud Terraform provider for infrastructure-as-code support.
- Add support for configuration versioning and rollback, maybe a GIT based workflow.
- Implement a GUI for manual conflict resolution.
- Add logging and audit trails for configuration changes.
- Support bulk operations for managing multiple environments simultaneously.

## 9. References
